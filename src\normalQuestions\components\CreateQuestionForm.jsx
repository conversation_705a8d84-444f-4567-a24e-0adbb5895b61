import React, { useState, useEffect, useRef } from "react";
import { Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { createQuestion } from "../../redux/slice/questionSlice.js";
import { createOption } from "../../redux/slice/optionsSlice.js"; // Import the createOption function
import { getCourses } from "../../redux/slice/courseSlice.js";
import toast, { Toaster } from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import {
  Form,
  Button,
  Row,
  Col,
  Card,
  Container,
  Modal,
} from "react-bootstrap";
import Select from "react-select";
import axios from "axios";
import { FaPlus } from "react-icons/fa";
import imageCompression from 'browser-image-compression';
import OptionModal from "../../commonComponents/OptionModal.jsx";
import ConfirmationModal from "../../commonComponents/ConfirmationModal.jsx";
import MathEditor from "../../CommonComponents/MathEditor";
import MathTextRenderer from "../../CommonComponents/MathTextRenderer";
import RichTextEditor from "../../CommonComponents/RichTextEditor";

export default function CreateQuestionForm({
  onQuestionContentChange,
  onOptionAdd,
}) {
  const [questionContent, setQuestionContent] = useState("");
  const [questionSlug, setQuestionSlug] = useState(null);
  const [explanation, setExplanation] = useState(""); // State for explanation

  // State for explanation image
  const [explanationImage, setExplanationImage] = useState(null);
  const [explanationImagePreview, setExplanationImagePreview] = useState(null);
  const [explanationImageError, setExplanationImageError] = useState("");
  const [explanationImageSizeText, setExplanationImageSizeText] = useState("");
  const [isCheckingExplanationImage, setIsCheckingExplanationImage] = useState(false);
  const explanationImageInputRef = useRef(null);

  // State for explanation math content
  const [explanationMathContent, setExplanationMathContent] = useState("");
  const [showExplanationMathEditor, setShowExplanationMathEditor] = useState(false);

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    setQuestionContent(newContent);
    onQuestionContentChange(newContent); // Pass the new content to the parent
    setFormData({ ...formData, content: e.target.value });
  };

  const accessToken = useSelector(
    (state) => state.contributor.accessToken || null
  );
  const contributorProfileId = useSelector(
    (state) => state.contributor.contributorProfileId || null
  );

  const [courses, setCourses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchCourses = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getCourses()); // Dispatch the action to fetch courses
      if (response && response.payload) {
        const coursesData = response.payload; // Assuming the data is in the payload
        setCourses(coursesData); // Set the courses data in the state
      }
    } catch (error) {
      console.error("Error fetching courses:", error);
      toast.error("Failed to load courses"); // Show error notification if fetching fails
    } finally {
      setIsLoading(false); // Set loading to false when the fetch process is complete
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  const dispatch = useDispatch();
  const navigate = useNavigate();

  // State to hold form data
  const [formData, setFormData] = useState({
    content: "",
    difficulty: 3,
    author: contributorProfileId,
    status: "active",
    // current_affairs: null,
    // is_current_affairs: false,
    approval_status: "pending",
    average_score: 0.0,
    times_attempted: 0,
    subject: [],
    subject_name: [],
    topic: [],
    topic_name: [],
    sub_topic: [],
    sub_topic_name: [],
    language: [],
    course: [],
    subcourse: [],
    // attachment: null,
    // master_question: null,
    // is_master: false,
    // master_option: null,
    // is_master_option: false,
  });

  // for options
  const [optionFormData, setOptionFormData] = useState({
    optionText: "",
    optionExplanation: "",
    isCorrect: false,
  });

  // for option images
  const [optionImage, setOptionImage] = useState(null);
  const [optionImageError, setOptionImageError] = useState("");
  const [optionImagePreview, setOptionImagePreview] = useState(null);
  const [isCheckingOptionImage, setIsCheckingOptionImage] = useState(false);
  const [optionImageSizeText, setOptionImageSizeText] = useState("");
  const [resetOptionImageInput, setResetOptionImageInput] = useState(false);

  const handleOptionInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (type === "radio") {
      setOptionFormData((prev) => ({
        ...prev,
        isCorrect: checked ? value : prev.isCorrect,
      }));
    } else {
      setOptionFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleOptionImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingOptionImage(true);
    setOptionImageError("");
    setOptionImage(null);
    setOptionImagePreview(null);
    setOptionImageSizeText("");

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setOptionImage(file);
      const reader = new FileReader();
      reader.onload = () => setOptionImagePreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingOptionImage(false);
      setOptionImageSizeText(`Image size: ${originalSizeKB} KB`);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_option_" + file.name;
              const compressedFileAsFile = new File([compressedFile], fileName, {
                type: compressedFile.type,
              });

              setOptionImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setOptionImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              setOptionImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setOptionImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Option image compression failed:", error);
            setOptionImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            setIsCheckingOptionImage(false);
          });
      } catch (error) {
        console.error("Error handling option image change:", error);
        setOptionImageError("An error occurred while processing the image.");
        setIsCheckingOptionImage(false);
      }
    }
  };

  // for getting the courses

  const [selectedCourses, setSelectedCourses] = useState([]);
  const [selectedSubcourses, setSelectedSubcourses] = useState({});
  const [subcourseOptions, setSubcourseOptions] = useState({});
  const [showModal, setShowModal] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [optionText, setOptionText] = useState("");
  const [isCorrect, setIsCorrect] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // for getting the Subject, Topic and Sub-topis

  const [subjects, setSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState([]); // Initialize as an empty array
  const [selectedTopic, setSelectedTopic] = useState([]);
  const [selectedSubtopic, setSelectedSubtopic] = useState([]);
  const [topics, setTopics] = useState([]);
  const [subtopics, setSubtopics] = useState([]);
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [preview, setPreview] = useState(null);
  const [isCheckingImage, setIsCheckingImage] = useState(false);
  const [imageSizeText, setImageSizeText] = useState(""); 

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingImage(true);
    setImageError("");
    setImage(null);
    setPreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setImage(file);
      const reader = new FileReader();
      reader.onload = () => setPreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.20,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options).then((compressedFile) => {
          const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
          console.log("Compressed file size:", compressedFile.size); // Debugging

          if (compressedFile.size <= 200 * 1024) {
            // Convert Blob to File (required for form data)
            const fileName = "compressed_" + file.name;
            const compressedFileAsFile = new File([compressedFile], fileName, {
              type: compressedFile.type,
            });

            console.log("Setting compressed image:", compressedFileAsFile);

            // Set the image as File object
            setImage(compressedFileAsFile);

            const reader = new FileReader();
            reader.onload = () => setPreview(reader.result);
            reader.readAsDataURL(compressedFileAsFile);

            // Display the image sizes in the UI
            setImageSizeText(
              `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
            );
          } else {
            setImageError(`Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`);
          }
        }).catch((error) => {
          console.error("Image compression failed:", error);
          setImageError("An error occurred while compressing the image.");
        }).finally(() => {
          setIsCheckingImage(false);
        });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        setIsCheckingImage(false);
      }
    }
  };

  const handleExplanationImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    setIsCheckingExplanationImage(true);
    setExplanationImageError("");
    setExplanationImage(null);
    setExplanationImagePreview(null);
    setExplanationImageSizeText("");

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      // Directly set the image if it is small enough
      setExplanationImage(file);
      const reader = new FileReader();
      reader.onload = () => setExplanationImagePreview(reader.result);
      reader.readAsDataURL(file);
      setIsCheckingExplanationImage(false);
      setExplanationImageSizeText(`Image size: ${originalSizeKB} KB`);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_explanation_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              setExplanationImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setExplanationImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              setExplanationImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setExplanationImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Explanation image compression failed:", error);
            setExplanationImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            setIsCheckingExplanationImage(false);
          });
      } catch (error) {
        console.error("Error handling explanation image change:", error);
        setExplanationImageError("An error occurred while processing the image.");
        setIsCheckingExplanationImage(false);
      }
    }
  };

  // Fetch subjects on component mount
  // Fetch subjects on component mount
  useEffect(() => {
    axios
      .get(`${import.meta.env.VITE_BASE_URL}api/questions/subjects/`) // Replace with your API URL
      .then((response) => {
        if (response.data.success) {
          setSubjects(response.data.data); // Set the subjects from the response
          // console.log("subjects", response.data.data);
        }
      })
      .catch((error) => {
        console.error("Error fetching subjects:", error);
      });
  }, []);

  // Handle Subject Change
  const handleSubjectChange = (selectedOptions) => {
    const subjectSlugs = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    const subjectNames = selectedOptions
      ? selectedOptions.map((option) => option.label)
      : [];
    setSelectedSubject(subjectSlugs);

    // Set the subject names in the form data
    setFormData({
      ...formData,
      subject: subjectSlugs,
      subject_name: subjectNames, // Add subject names here
    });

    // Filter topics based on selected subjects
    const filteredTopics = subjects
      .filter((subject) => subjectSlugs.includes(subject.slug))
      .map((subject) => subject.topics)
      .flat();

    setTopics(filteredTopics);
  };

  // Handle Topic Change
  const handleTopicChange = (selectedOptions) => {
    const topicSlugs = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    const topicNames = selectedOptions
      ? selectedOptions.map((option) => option.label)
      : [];
    setSelectedTopic(topicSlugs);

    // Set the topic names in the form data
    setFormData({
      ...formData,
      topic: topicSlugs,
      topic_name: topicNames, // Add topic names here
    });

    // Filter subtopics based on selected topics
    const filteredSubtopics = topics
      .filter((topic) => topicSlugs.includes(topic.slug))
      .map((topic) => topic.subtopics)
      .flat();

    setSubtopics(filteredSubtopics);
  };

  // Handle Subtopic Change
  const handleSubtopicChange = (selectedOptions) => {
    const subtopicSlugs = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    const subtopicNames = selectedOptions
      ? selectedOptions.map((option) => option.label)
      : [];
    setSelectedSubtopic(subtopicSlugs);

    // Set the subtopic names in the form data
    setFormData({
      ...formData,
      sub_topic: subtopicSlugs,
      sub_topic_name: subtopicNames, // Add subtopic names here
    });
  };

  // Options for subject dropdown
  const subjectOptions = subjects.map((subject) => ({
    value: subject.slug, // Use 'slug' as the value
    label: subject.name,
  }));

  // Options for topic dropdown (filtering topics based on selected subjects)
  const topicOptions = topics.map((topic) => ({
    value: topic.slug, // Use 'slug' as the value
    label: topic.name,
  }));

  // Options for subtopic dropdown (filtering subtopics based on selected topics)
  const subtopicOptions = subtopics.map((subtopic) => ({
    value: subtopic.slug, // Use 'slug' as the value
    label: subtopic.name,
  }));

  useEffect(() => {
    if (!accessToken) {
      toast.error("Please log in as a contributor!");
      const timer = setTimeout(() => {
        navigate("/contributor_login");
      }, 2000);
      return () => clearTimeout(timer);
    }
    dispatch(getCourses());
  }, [accessToken, dispatch, navigate]);

  const handleCourseChange = (selectedOptions) => {
    const courseIds = selectedOptions
      ? selectedOptions.map((option) => option.value)
      : [];
    setSelectedCourses(courseIds);
    setFormData({
      ...formData,
      course: courseIds,
      subcourse: [], // Reset subcourses when courses change
    });
  };

  const handleSelectAllCourses = (selectedOptions) => {
    if (selectedOptions.some((option) => option.value === "selectAll")) {
      setSelectedCourses(courses.map((course) => course.slug)); // Select all courses
    } else {
      setSelectedCourses(selectedOptions.map((option) => option.value)); // Keep other selections
    }
  };

  const handleSelectAllSubcourses = (courseId, selectedOptions) => {
    if (selectedOptions.some((option) => option.value === "selectAll")) {
      setSelectedSubcourses({
        ...selectedSubcourses,
        [courseId]: subcourseOptions[courseId]?.map((option) => option.value),
      });
    } else {
      const subcourseIds = selectedOptions.map((option) => option.value);
      setSelectedSubcourses({
        ...selectedSubcourses,
        [courseId]: subcourseIds,
      });
    }
  };

  const handleGetSubcourses = async () => {
    const payload = { course_slugs: selectedCourses };
    try {
      const response = await axios.post(
        `${
          import.meta.env.VITE_BASE_URL
        }api/questions/courses-with-sub-courses/`,
        payload
      );
      const data = response.data;
      const newSubcourseOptions = {};

      data.forEach((course) => {
        newSubcourseOptions[course.slug] = course.sub_courses.map(
          (subcourse) => ({
            value: subcourse.slug,
            label: subcourse.name,
          })
        );
      });

      setSubcourseOptions(newSubcourseOptions);
    } catch (error) {
      toast.error("Please select at least one course");
      console.error(error);
    }
  };

const handleSubmit = async (e) => {
  e.preventDefault();

  // Check if subject, topic, and subtopic IDs are selected
  if (
    !formData.content ||
    !formData.subject ||
    !formData.topic ||
    selectedCourses.length === 0 ||
    formData.language === ""
  ) {
    toast.error("Please fill in all required fields");
    return;
  }

  // Preparing final payload for creating a question
  const payload = {
    ...formData,
    subject: formData.subject, // Array of selected subjects
    subject_name: formData.subject_name, // Include subject names
    topic: formData.topic, // Array of selected topics
    topic_name: formData.topic_name, // Include topic names
    sub_topic: formData.sub_topic, // Array of selected subtopics
    sub_topic_name: formData.sub_topic_name, // Include subtopic names
    course: selectedCourses, // Set selected course IDs
    subcourse: Object.values(selectedSubcourses).flat(), // Flatten selected subcourse IDs
    ...(image && { attachments: image }), // Include the image if available
    explanation, // Add explanation to the payload
    ...(explanationImage && { explanation_attachment: explanationImage }), // Include explanation image if available
  };

  // console.log("Form Data:", payload); 

  try {
    const resultAction = await dispatch(createQuestion({ data: payload }));
    
    // Extract meta and payload separately
    const { meta, payload: actionResult } = resultAction;

    // Check if the response has a 226 status code
    if (actionResult?.error) {     
      toast.error(actionResult?.error ||"Similar question already exist" );
      setShowModal(false); // Show modal for adding options    
      return; 
    } 

    // Check if the response has a slug
    if (actionResult?.slug) {
      setQuestionSlug(actionResult.slug);
      toast.success("Question created successfully!");
      setShowModal(true); // Show modal for adding options
      console.log("Question ID:", actionResult.slug);
    } else {
      throw new Error("Slug is missing from the response");
    }
  } catch (error) {
    console.error("Error creating question:", error);
    toast.error(error?.message || "Failed to create question");
  }
};

  

  const languageOptions = [
    { value: "english", label: "English" },
    { value: "hindi", label: "Hindi" },
  ];

  const courseOptionsWithSelectAll = [
    { value: "selectAll", label: "Select All Courses" },
    ...courses.map((course) => ({
      value: course.slug,
      label: course.name,
    })),
  ];

  // Now `questionSlug` is available and can be used for adding options
  const handleAddOption = async () => {
    // Disable the button to prevent multiple submissions
    setIsSubmitting(true);

    if (!questionSlug) {
      toast.error("Please create a question first");
      setIsSubmitting(false); // Re-enable the button if no questionSlug
      return;
    }

    const optionPayload = {
      option_text: optionFormData.optionText, // This now contains embedded math with $ delimiters
      option_explanation: optionFormData.optionExplanation, // Option explanation with embedded math
      is_correct: optionFormData.isCorrect === "true", // Convert string to boolean
      ...(optionImage && { attachments: optionImage }), // Include the option image if available
    };



    try {
      await dispatch(
        createOption({ payload: optionPayload, questionSlug })
      ).unwrap();
      toast.success("Option added successfully!");
      setOptionFormData({
        ...optionFormData,
        optionText: "",
        optionExplanation: "",
        isCorrect: false,
      });
      // Reset option image states
      setOptionImage(null);
      setOptionImagePreview(null);
      setOptionImageError("");
      setOptionImageSizeText("");
      setResetOptionImageInput(prev => !prev); // Toggle to trigger file input reset
      setShowModal(true); // Keep the modal open after adding an option
      onOptionAdd(); // Optional callback for any additional behavior
    } catch (error) {
      toast.error(error.message || "Failed to add option");
    } finally {
      // Re-enable the button after the request is finished
      setIsSubmitting(false);
    }
  };

  const handleCloseModal = () => {
    setShowConfirmation(true); // Show confirmation dialog
  };

  const handleConfirmClose = () => {
    setShowConfirmation(false);
    setShowModal(false); // Close modal

    // Reset option form data
    setOptionFormData({
      optionText: "",
      isCorrect: "false",
    });

    // Reset option image states
    setOptionImage(null);
    setOptionImagePreview(null);
    setOptionImageError("");
    setOptionImageSizeText("");
    setResetOptionImageInput(prev => !prev); // Toggle to trigger file input reset

    // Reset the main form data
    // setFormData({
    //   content: "",
    //   difficulty: 3,
    //   author: contributorProfileId,
    //   status: "active",
    //   approval_status: "pending",
    //   average_score: 0.0,
    //   times_attempted: 0,
    //   subject: [],
    //   subject_name: [],
    //   topic: [],
    //   topic_name: [],
    //   sub_topic: [],
    //   sub_topic_name: [],
    //   language: [],
    //   course: [],
    //   subcourse: [],
    // });

    // Reset selected states
    // setSelectedCourses([]);
    // setSelectedSubcourses({});
    // setSelectedSubject(null);
    // setSelectedTopic(null);
    // setSelectedSubtopic(null);

    // Reset image-related states
    setPreview(null); // Clear image preview
    setImageError(null); // Clear any error messages

    // Reset the file input field
    if (imageInputRef.current) {
      imageInputRef.current.value = "";
    }
  };
  
  const handleCancelClose = () => {
    setShowConfirmation(false); // Cancel close
  };



  // State for math content
  const [mathContent, setMathContent] = useState("");
  const [showMathEditor, setShowMathEditor] = useState(false);

  return (
    <>
      <Container className="mt-4">
        <Row>
          <Col>
            <Card className="shadow-lg p-4">
              <Form onSubmit={handleSubmit}>
                <Form.Group controlId="content" className="mb-3">
                  <RichTextEditor
                    label="Question Content"
                    name="content"
                    value={formData.content}
                    onChange={handleContentChange}
                    placeholder="Enter the question content"
                    rows={3}
                    required
                  />
                </Form.Group>

                {/* Math Editor Section */}
                <Form.Group className="mb-3">
                  <div className="d-flex justify-content-between align-items-center mb-2">
                    <Form.Label>Add Mathematical Expressions (Optional)</Form.Label>
                    <Button
                      variant="outline-primary"
                      size="sm"
                      onClick={() => setShowMathEditor(!showMathEditor)}
                    >
                      {showMathEditor ? 'Hide' : 'Show'} Math Editor
                    </Button>
                  </div>

                  {showMathEditor && (
                    <MathEditor
                      value={mathContent}
                      onChange={setMathContent}
                      label="Mathematical Expression"
                      placeholder="Enter mathematical expressions, formulas, equations..."
                      showPreview={true}
                      showRawLatex={false}
                      displayMode={true}
                      embeddedMode={true}
                      textContent={formData.content}
                      onTextContentChange={(newContent) => setFormData({...formData, content: newContent})}
                      className="mb-3"
                    />
                  )}

                  {/* Preview of content with embedded math */}
                  {formData.content && (
                    <div className="mt-2">
                      <Form.Label>Content Preview:</Form.Label>
                      <div className="border rounded p-2 bg-light">
                        <MathTextRenderer text={formData.content} />
                      </div>
                    </div>
                  )}
                </Form.Group>

                <Row>
                  <Col>
                    <Form.Group controlId="difficulty">
                      <Form.Label className="pb-3 pt-1">
                        Difficulty Level
                      </Form.Label>
                      <Form.Select
                        name="difficulty"
                        value={formData.difficulty}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            difficulty: e.target.value,
                          })
                        }
                        required
                      >
                        {[...Array(11).keys()].map((level) => (
                          <option key={level} value={level}>
                            {level}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>

                  <Col className="mt-1">
                    {/* Subject Dropdown */}
                    <Form.Group controlId="subject">
                      <Form.Label>
                        Subject
                        <Link to="/subjects_dashboard">
                          <Button
                            variant="outline-success"
                            style={{ marginLeft: "1rem" }}
                          >
                            <FaPlus /> {/* Add the Plus icon */}
                          </Button>
                        </Link>
                      </Form.Label>
                      <Select
                        options={subjectOptions}
                        onChange={handleSubjectChange}
                        value={subjectOptions.filter((option) =>
                          selectedSubject?.includes(option.value)
                        )} // This filters the selected subjects
                        isMulti
                        placeholder="Select Subject"
                      />
                    </Form.Group>
                  </Col>
                </Row>

                <Row className="mb-3 mt-2">
                  <Col>
                    {/* Topic Dropdown */}
                    {selectedSubject && (
                      <Form.Group controlId="topic">
                        <Form.Label>Topic</Form.Label>
                        <Select
                          options={topicOptions}
                          onChange={handleTopicChange}
                          // value={selectedTopic}
                          value={topicOptions.filter((option) => selectedTopic?.includes(option.value))}
                          isMulti
                          placeholder="Select Topic"
                        />
                      </Form.Group>
                    )}
                  </Col>

                  <Col>
                    {selectedTopic && (
                      <Form.Group controlId="subtopic">
                        <Form.Label>Subtopic</Form.Label>
                        <Select
                          options={subtopicOptions}
                          onChange={handleSubtopicChange}
                          // value={selectedSubtopic}
                          value={subtopicOptions.filter((option) => selectedSubtopic?.includes(option.value))}
                          isMulti
                          placeholder="Select Subtopic"
                        />
                      </Form.Group>
                    )}
                  </Col>
                </Row>

                <Form.Group controlId="course" className="mb-3 mt-2">
                  <Form.Label>
                    Select Course
                    <Link to="/add_courses">
                      <Button
                        variant="outline-success"
                        style={{ marginLeft: "1rem" }}
                      >
                        <FaPlus /> {/* Add the Plus icon */}
                      </Button>
                    </Link>
                  </Form.Label>
                  <Select
                    options={courseOptionsWithSelectAll}
                    isMulti
                    value={courseOptionsWithSelectAll.filter((option) =>
                      selectedCourses.includes(option.value)
                    )}
                    onChange={handleSelectAllCourses}
                    placeholder="Select courses"
                  />
                </Form.Group>

                <div>
                  <Button
                    variant="outline-primary w-100 text-center"
                    onClick={handleGetSubcourses}
                    className="mb-3"
                  >
                    Get Subcourses
                  </Button>
                </div>

                {selectedCourses.map((courseId) => (
                  <Form.Group
                    key={courseId}
                    controlId={`subcourse-${courseId}`}
                    className="mb-3"
                  >
                    <Form.Label>
                      Select Subcourses for{" "}
                      {courses.find((course) => course.slug === courseId)?.name}
                    </Form.Label>
                    <Select
                      options={[
                        { value: "selectAll", label: "Select All Subcourses" },
                        ...(subcourseOptions[courseId] || []),
                      ]}
                      isMulti
                      value={
                        selectedSubcourses[courseId]?.map((id) => ({
                          value: id,
                          label: subcourseOptions[courseId]?.find(
                            (sub) => sub.value === id
                          )?.label,
                        })) || []
                      }
                      onChange={(selectedOptions) =>
                        handleSelectAllSubcourses(courseId, selectedOptions)
                      }
                      placeholder="Select subcourses"
                    />
                  </Form.Group>
                ))}

                <Row>
                  <Col>
                    <Form.Group controlId="language">
                      <Form.Label>Select Language</Form.Label>
                      <Select
                        options={languageOptions}
                        value={languageOptions.find(
                          (option) => option.value === formData.language
                        )}
                        onChange={(selectedOption) =>
                          setFormData({
                            ...formData,
                            language: selectedOption.value,
                          })
                        }
                        placeholder="Language"
                        required
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Form.Group controlId="questionImage" className="my-3">
                      {imageSizeText && (
                        <p className="text-success">{imageSizeText}</p>
                      )}

                      {imageError && (
                        <p className="text-danger mb-2">{imageError}</p>
                      )}
                      <Form.Label>Question Image (Under 200 KB)</Form.Label>
                      <Form.Control
                        ref={imageInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                      />
                    </Form.Group>

                    {preview && (
                      <div className="mb-3">
                        <img
                          src={preview}
                          alt="Preview"
                          style={{
                            width: "100%",
                            maxHeight: "200px",
                            objectFit: "cover",
                          }}
                        />
                      </div>
                    )}
                  </Col>
                </Row>

                <Row>
                  <Col>
                    <Form.Group controlId="explanation" className="mb-3">
                      <RichTextEditor
                        label="Explanation"
                        name="explanation"
                        value={explanation}
                        onChange={(e) => setExplanation(e.target.value)}
                        placeholder="Enter explanation for the question"
                        rows={3}
                      />
                    </Form.Group>
                  </Col>
                </Row>

                {/* Math Editor Section for Explanation */}
                <Row>
                  <Col>
                    <Form.Group className="mb-3">
                      <div className="d-flex justify-content-between align-items-center mb-2">
                        <Form.Label>Add Mathematical Expressions to Explanation (Optional)</Form.Label>
                        <Button
                          variant="outline-primary"
                          size="sm"
                          onClick={() => setShowExplanationMathEditor(!showExplanationMathEditor)}
                        >
                          {showExplanationMathEditor ? 'Hide' : 'Show'} Math Editor
                        </Button>
                      </div>

                      {showExplanationMathEditor && (
                        <MathEditor
                          value={explanationMathContent}
                          onChange={setExplanationMathContent}
                          label="Mathematical Expression for Explanation"
                          placeholder="Enter mathematical expressions, formulas, equations for explanation..."
                          showPreview={true}
                          showRawLatex={false}
                          displayMode={true}
                          embeddedMode={true}
                          textContent={explanation}
                          onTextContentChange={setExplanation}
                          className="mb-3"
                        />
                      )}

                      {/* Preview of explanation with embedded math */}
                      {explanation && (
                        <div className="mt-2">
                          <Form.Label>Explanation Preview:</Form.Label>
                          <div className="border rounded p-3 bg-light">
                            <MathTextRenderer text={explanation} />
                          </div>
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>
                <Row>
                  <Col>
                    <Form.Group controlId="explanationImage" className="mb-3">
                      {explanationImageSizeText && (
                        <p className="text-success">{explanationImageSizeText}</p>
                      )}
                      {explanationImageError && (
                        <p className="text-danger mb-2">{explanationImageError}</p>
                      )}
                      <Form.Label>Explanation Image (Optional, Under 200 KB)</Form.Label>
                      <Form.Control
                        ref={explanationImageInputRef}
                        type="file"
                        accept="image/*"
                        onChange={handleExplanationImageChange}
                        disabled={isCheckingExplanationImage}
                      />
                      {isCheckingExplanationImage && (
                        <p className="text-info small mt-1">Processing image...</p>
                      )}
                      {explanationImagePreview && (
                        <div className="mt-3">
                          <img
                            src={explanationImagePreview}
                            alt="Explanation Preview"
                            style={{
                              width: "100%",
                              maxHeight: "200px",
                              objectFit: "cover",
                            }}
                          />
                        </div>
                      )}
                    </Form.Group>
                  </Col>
                </Row>

                <Row>
                  <Col className="d-flex align-items-end">
                    <Button
                      variant="outline-success"
                      type="submit"
                      className="w-100"
                    >
                      Submit
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
          </Col>
        </Row>
      </Container>

      <OptionModal  // Use the OptionModal component
        show={showModal}
        handleClose={handleCloseModal}
        optionFormData={optionFormData}
        handleOptionInputChange={handleOptionInputChange}
        handleAddOption={handleAddOption}
        isSubmitting={isSubmitting}
        // New props for option image handling
        optionImagePreview={optionImagePreview}
        handleOptionImageChange={handleOptionImageChange}
        optionImageError={optionImageError}
        optionImageSizeText={optionImageSizeText}
        isCheckingOptionImage={isCheckingOptionImage}
        resetOptionImageInput={resetOptionImageInput}
      />

      <ConfirmationModal  // Use the ConfirmationModal component
        show={showConfirmation}
        handleCancelClose={handleCancelClose}
        handleConfirmClose={handleConfirmClose}
      />

    </>
  );
}
