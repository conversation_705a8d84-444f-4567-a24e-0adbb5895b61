import React, { useRef, useEffect, useState } from 'react';
import { Modal, Form, Button } from 'react-bootstrap';
import MathEditor from './MathEditor';
import MathTextRenderer from './MathTextRenderer';
import RichTextEditor from './RichTextEditor';

const OptionModal = ({
  show,
  handleClose,
  optionFormData,
  handleOptionInputChange,
  handleAddOption,
  isSubmitting,
  // New props for image handling
  optionImagePreview,
  handleOptionImageChange,
  optionImageError,
  optionImageSizeText,
  isCheckingOptionImage,
  // New prop for resetting file input
  resetOptionImageInput
}) => {
  const optionImageInputRef = useRef(null);
  const [localMathContent, setLocalMathContent] = useState("");
  const [showOptionMathEditor, setShowOptionMathEditor] = useState(false);

  // Reset file input when resetOptionImageInput changes
  useEffect(() => {
    if (resetOptionImageInput && optionImageInputRef.current) {
      optionImageInputRef.current.value = "";
    }
  }, [resetOptionImageInput]);

  // Reset math content when modal closes
  useEffect(() => {
    if (!show) {
      setLocalMathContent("");
      setShowOptionMathEditor(false);
    }
  }, [show]);

  return (
    <Modal
      show={show}
      onHide={handleClose}
      backdrop="static"
      keyboard={false}
      centered
      style={{
        position: 'fixed',
        top: '50%',
        left: '10%',
        transform: 'translateY(-50%)',
        margin: '0',
        width: '600px', // Increased width to accommodate math editor and image upload
      }}
    >
      <Modal.Header closeButton>
        <Modal.Title>Add Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form.Group className="mb-3">
          <RichTextEditor
            label="Option Text"
            name="optionText"
            value={optionFormData.optionText}
            onChange={handleOptionInputChange}
            placeholder="Enter option text"
            rows={2}
            required
          />
        </Form.Group>

        {/* Math Editor Section for Options */}
        <Form.Group className="mb-3">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <Form.Label>Mathematical Content (Optional)</Form.Label>
            <Button
              variant="outline-primary"
              size="sm"
              onClick={() => setShowOptionMathEditor(!showOptionMathEditor)}
            >
              {showOptionMathEditor ? 'Hide' : 'Add'} Math
            </Button>
          </div>

          {showOptionMathEditor && (
            <MathEditor
              value={localMathContent}
              onChange={setLocalMathContent}
              label="Mathematical Expression"
              placeholder="Enter mathematical expressions for this option..."
              showPreview={true}
              showRawLatex={false}
              displayMode={false}
              embeddedMode={true}
              textContent={optionFormData.optionText}
              onTextContentChange={(newText) => handleOptionInputChange({
                target: { name: 'optionText', value: newText }
              })}
              className="mb-2"
            />
          )}

          {/* Preview of option text with embedded math */}
          {optionFormData.optionText && (
            <div className="mt-2">
              <Form.Label>Option Preview:</Form.Label>
              <div className="border rounded p-2 bg-light small">
                <MathTextRenderer text={optionFormData.optionText} />
              </div>
            </div>
          )}
        </Form.Group>

        {/* Option Explanation */}
        <Form.Group className="mb-3">
          <RichTextEditor
            label="Option Explanation (Optional)"
            name="optionExplanation"
            value={optionFormData.optionExplanation || ""}
            onChange={handleOptionInputChange}
            placeholder="Enter explanation for this option"
            rows={2}
          />
        </Form.Group>

        {/* Math Editor Section for Option Explanation */}
        <Form.Group className="mb-3">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <Form.Label>Mathematical Content for Explanation (Optional)</Form.Label>
            <Button
              variant="outline-secondary"
              size="sm"
              onClick={() => setShowOptionMathEditor(!showOptionMathEditor)}
            >
              {showOptionMathEditor ? 'Hide' : 'Add'} Math to Explanation
            </Button>
          </div>

          {showOptionMathEditor && (
            <MathEditor
              value={localMathContent}
              onChange={setLocalMathContent}
              label="Mathematical Expression for Option Explanation"
              placeholder="Enter mathematical expressions for option explanation..."
              showPreview={true}
              showRawLatex={false}
              displayMode={false}
              embeddedMode={true}
              textContent={optionFormData.optionExplanation || ""}
              onTextContentChange={(newText) => handleOptionInputChange({
                target: { name: 'optionExplanation', value: newText }
              })}
              className="mb-2"
            />
          )}

          {/* Preview of option explanation with embedded math */}
          {optionFormData.optionExplanation && (
            <div className="mt-2">
              <Form.Label>Option Explanation Preview:</Form.Label>
              <div className="border rounded p-2 bg-light small">
                <MathTextRenderer text={optionFormData.optionExplanation} />
              </div>
            </div>
          )}
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>Is Correct Answer?</Form.Label>
          <div>
            <Form.Check
              type="radio"
              label="Yes"
              name="isCorrect"
              value="true"
              checked={optionFormData.isCorrect === "true"}
              onChange={handleOptionInputChange}
            />
            <Form.Check
              type="radio"
              label="No"
              name="isCorrect"
              value="false"
              checked={optionFormData.isCorrect === "false"}
              onChange={handleOptionInputChange}
            />
          </div>
        </Form.Group>

        <Form.Group className="mb-3">
          <Form.Label>Option Image (Optional, Under 200 KB)</Form.Label>
          {optionImageSizeText && (
            <p className="text-success small">{optionImageSizeText}</p>
          )}
          {optionImageError && (
            <p className="text-danger small mb-2">{optionImageError}</p>
          )}
          <Form.Control
            ref={optionImageInputRef}
            type="file"
            accept="image/*"
            onChange={handleOptionImageChange}
            disabled={isCheckingOptionImage}
          />
          {isCheckingOptionImage && (
            <p className="text-info small mt-1">Processing image...</p>
          )}
        </Form.Group>

        {/* Image Preview */}
        {optionImagePreview && (
          <div className="mb-3">
            <Form.Label>Image Preview</Form.Label>
            <div>
              <img
                src={optionImagePreview}
                alt="Option Preview"
                style={{
                  width: "100%",
                  maxHeight: "150px",
                  objectFit: "cover",
                  border: "1px solid #ddd",
                  borderRadius: "4px"
                }}
              />
            </div>
          </div>
        )}

        <Button
          variant="success"
          onClick={handleAddOption}
          disabled={isSubmitting || isCheckingOptionImage}
          className="w-100"
        >
          {isSubmitting ? 'Adding Option...' : 'Add Option'}
        </Button>
      </Modal.Body>
    </Modal>
  );
};

export default OptionModal;
