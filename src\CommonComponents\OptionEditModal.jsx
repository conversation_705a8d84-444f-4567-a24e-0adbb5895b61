import React from "react";
import { Mo<PERSON>, <PERSON>, Button } from "react-bootstrap";
import RichTextEditor from "./RichTextEditor";

const OptionEditModal = ({  show,  handleClose,  updatedOptionData,  setUpdatedOptionData,  handleSubmitOption,}) => {
  return (
    <Modal show={show} onHide={handleClose} centered>
      <Modal.Header closeButton>
        <Modal.Title>Edit Option</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <Form onSubmit={handleSubmitOption}>
          <Form.Group controlId="optionText">
            <RichTextEditor
              label="Option Text"
              name="option_text"
              value={updatedOptionData.option_text}
              onChange={(e) =>
                setUpdatedOptionData({
                  ...updatedOptionData,
                  option_text: e.target.value,
                })
              }
              placeholder="Enter option text"
              rows={3}
              rows={3}
              required
            />
          </Form.Group>



          <Form.Group controlId="isCorrect">
            <Form.Label>Is Correct</Form.Label>
            <Form.Check
              type="checkbox"
              name="is_correct"
              checked={updatedOptionData.is_correct}
              onChange={(e) =>
                setUpdatedOptionData({
                  ...updatedOptionData,
                  is_correct: e.target.checked,
                })
              }
            />
          </Form.Group>
          <Button variant="outline-primary" type="submit" className="mt-3 w-100">
            Save Changes
          </Button>
        </Form>
      </Modal.Body>
    </Modal>
  );
};

export default OptionEditModal;
