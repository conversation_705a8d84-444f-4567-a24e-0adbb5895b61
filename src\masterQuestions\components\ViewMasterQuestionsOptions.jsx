import React, { useEffect, useRef, useState } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getMasterQuestion, deleteMasterQuestion, updateMasterQuestion } from "../../redux/slice/masterQuestionSlice";
import { deleteQuestion, updateQuestion } from "../../redux/slice/questionSlice";
import { deleteOption, updateOption } from "../../redux/slice/optionsSlice";
import { Container, Form, Card, Button, Modal } from "react-bootstrap";
import { FaEdit, FaTrashAlt, FaMinus, FaQuestionCircle, FaRegCircle } from "react-icons/fa";
import { IoEllipsisHorizontalCircleSharp, IoFilterCircle } from "react-icons/io5";

import Swal from "sweetalert2";
import { toast, Toaster } from "react-hot-toast";
import imageCompression from "browser-image-compression";
import QuestionListSkeleton from "../../commonComponents/QuestionListSkeleton";
import RichTextEditor from "../../CommonComponents/RichTextEditor";
import MathTextRenderer from "../../CommonComponents/MathTextRenderer";
import OptionEditModal from "../../CommonComponents/OptionEditModal";

export default function ViewMasterQuestionsOptions({ optionAddedFlag }) {
  const { slug } = useParams();
  const dispatch = useDispatch();
  const contributorProfileId = useSelector(
    (state) => state.contributor.contributorProfileId || null
  );

  const [masterQuestion, setMasterQuestion] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showQuestionModal, setShowQuestionModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [updatedData, setUpdatedData] = useState({
    author: contributorProfileId,
    title: "",
    passage_content: "",
  });

  // Add these new state variables at the top with other state declarations
  const [newImage, setNewImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [imagePreview, setImagePreview] = useState(null);
  const [imageSizeText, setImageSizeText] = useState("");

  const imageInputRef = useRef(null);

  const handleImageChange = (e) => {
    const file = e.target.files[0];

    if (!file) return;

    // setIsCheckingImage(true);
    setImageError("");
    setNewImage(null);
    setImagePreview(null);

    const originalSizeKB = (file.size / 1024).toFixed(2);

    if (file.size <= 200 * 1024) {
      console.log("Image is less than 200kb: ", file);

      // Directly set the image if it is small enough
      setNewImage(file);
      const reader = new FileReader();
      reader.onload = () => setImagePreview(reader.result);
      reader.readAsDataURL(file);
      // setIsCheckingImage(false);
    } else {
      const options = {
        maxSizeMB: 0.2,
        maxWidthOrHeight: 300,
        useWebWorker: true,
      };

      try {
        imageCompression(file, options)
          .then((compressedFile) => {
            const compressedSizeKB = (compressedFile.size / 1024).toFixed(2);
            console.log("Compressed file size:", compressedFile.size); // Debugging

            if (compressedFile.size <= 200 * 1024) {
              // Convert Blob to File (required for form data)
              const fileName = "compressed_" + file.name;
              const compressedFileAsFile = new File(
                [compressedFile],
                fileName,
                {
                  type: compressedFile.type,
                }
              );

              console.log("Setting compressed image:", compressedFileAsFile);

              // Set the image as File object
              setNewImage(compressedFileAsFile);

              const reader = new FileReader();
              reader.onload = () => setImagePreview(reader.result);
              reader.readAsDataURL(compressedFileAsFile);

              // Display the image sizes in the UI
              setImageSizeText(
                `Original Size: ${originalSizeKB} KB, Compressed Size: ${compressedSizeKB} KB`
              );
            } else {
              setImageError(
                `Image exceeds 200KB even after compression. Original: ${originalSizeKB} KB, Compressed: ${compressedSizeKB} KB.`
              );
            }
          })
          .catch((error) => {
            console.error("Image compression failed:", error);
            setImageError("An error occurred while compressing the image.");
          })
          .finally(() => {
            // setIsCheckingImage(false);
          });
      } catch (error) {
        console.error("Error handling image change:", error);
        setImageError("An error occurred while processing the image.");
        // setIsCheckingImage(false);
      }
    }
  };

  // Function to fetch master questions
  const fetchMasterQuestions = async () => {
    setIsLoading(true);
    try {
      const response = await dispatch(getMasterQuestion(slug));
      if (response && response.payload) {
        setMasterQuestion(response.payload);
      } else {
        console.error("Invalid data format:", response.payload);
        toast.error("Failed to load questions, invalid data format.");
      }
    } catch (error) {
      console.error("Error fetching master questions:", error);
      toast.error("Failed to load questions");
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch master questions when component mounts or when `optionAddedFlag` changes
  useEffect(() => {
    fetchMasterQuestions();
  }, [optionAddedFlag]);

  // function to edit question

  const handleEdit = (question) => {
    setSelectedQuestion(question);
    setUpdatedData({
      author: contributorProfileId,
      title: question.title,
      passage_content: question.passage_content,
    });
    setShowModal(true);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setUpdatedData((prevData) => ({
      ...prevData,
      [name]: value,
      author: contributorProfileId,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (updatedData.title && updatedData.passage_content) {
      try {
        const actionResult = await dispatch(
          updateMasterQuestion({ slug: selectedQuestion.slug, updatedData })
        );

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          setShowModal(false);
          // Refetch questions after successful update
          fetchMasterQuestions();
          toast.success("Question updated successfully!");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to update the question.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error updating question:", error);
        toast.error(
          "An unexpected error occurred while updating the question."
        );
      }
    } else {
      toast.error("Please fill out all the required fields.");
    }
  };

  // Function to delete the master question
  const handleDelete = async (slug) => {
    if (!slug) {
      toast.error("ID is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        const actionResult = await dispatch(deleteMasterQuestion(slug));

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          // Refetch questions after successful deletion
          fetchMasterQuestions();
          Swal.fire("Deleted!", "The question has been deleted.", "success");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to delete the question.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error deleting the question:", error);
        toast.error(
          "An unexpected error occurred while deleting the question."
        );
      }
    }
  };

  // States for option edit modal
  const [showOptionModal, setShowOptionModal] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [updatedOptionData, setUpdatedOptionData] = useState({
    option_text: "",
    option_explanation: "",
    is_correct: false,
  });
  const [selectedQuestionSlug, setSelectedQuestionSlug] = useState(null); // Initialize the selected question ID state

  // state for updaing the question
  const [updatedQuestionData, setQuestionUpdatedQuestionData] = useState({
    content: "",
    difficulty: 3,
    author: contributorProfileId,
    status: "active",
    // current_affairs: null,
    // is_current_affairs: false,
    approval_status: "pending",
    average_score: 0.0,
    times_attempted: 0,
    subject: "",
    subject_name: "",
    topic: "",
    topic_name: "",
    sub_topic: "",
    sub_topic_name: "",
    language: "",
    course: [],
    subcourse: [],
    // master_question: null,
    // is_master: false,
    // master_option: null,
    // is_master_option: false,
    options: [],
  });

  // FUNCTION FOR EDIT THE QUESTION

  const handleQuestionInputChange = (e) => {
    const { name, value } = e.target;
    setQuestionUpdatedQuestionData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  // FUNCTION FOR EDIT THE QUESTION

  const handleEditQuestion = (question) => {
    setSelectedQuestion(question);
    setQuestionUpdatedQuestionData({
      content: question.content, // Only the question content is editable
      subject: question.subject, // Pre-set subject, topic, and subtopic
      subject_name: question.subject_name,
      topic: question.topic,
      topic_name: question.topic_name,
      sub_topic: question.sub_topic,
      sub_topic_name: question.sub_topic_name,
    });
    // Set initial image preview if question has attachment
    setImagePreview(
      question.attachments
        ? `${import.meta.env.VITE_BASE_URL}/${question.attachments}`
        : null
    );
    setNewImage(null); // Reset new image state
    console.log("SetNewImage is set to null");
    setShowQuestionModal(true); // Show modal for editing
  };

  const handleSubmitQuestion = async (e) => {
    e.preventDefault();

    try {
      const payload = {
        content: updatedQuestionData.content, // Only update the content
        subject: selectedQuestion.subject, // Keep the original subject
        subject_name: selectedQuestion.subject_name, // Keep the original subject name
        topic: selectedQuestion.topic, // Keep the original topic
        topic_name: selectedQuestion.topic_name, // Keep the original topic name
        sub_topic: selectedQuestion.sub_topic, // Keep the original sub-topic
        sub_topic_name: selectedQuestion.sub_topic_name, // Keep the original sub-topic name
        author: selectedQuestion.author,
        status: selectedQuestion.status,
        approval_status: selectedQuestion.approval_status,
        average_score: selectedQuestion.average_score,
        times_attempted: selectedQuestion.times_attempted,
        language: selectedQuestion.language,
        course: selectedQuestion.course,
        subcourse: selectedQuestion.subcourse,
        master_question: selectedQuestion.master_question,
        is_master: selectedQuestion.is_master,
      };

      if (newImage) {
        console.log("yes new image, passign attachemnt ", newImage);
        payload.attachments = newImage;
      }

      // Dispatch the updateQuestion action
      const actionResult = await dispatch(
        updateQuestion({
          questionSlug: selectedQuestion.slug, // Question ID to update
          data: payload,
        })
      );

      // Check if the action was fulfilled
      if (actionResult.meta.requestStatus === "fulfilled") {
        // Close modal and refresh questions
        setShowQuestionModal(false);
        fetchMasterQuestions(); // Refetch questions to show updated data
        toast.success("Question updated successfully!");
      } else {
        toast.error("Failed to update the question.");
      }
    } catch (error) {
      console.error("Error updating question", error);
      toast.error("Failed to update the question.");
    }
  };

  // Function to delete a question

  const handleDeleteQuestion = async (slug) => {
    if (!slug) {
      toast.error("ID is missing, cannot delete the item.");
      return;
    }

    const result = await Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    });

    if (result.isConfirmed) {
      try {
        const actionResult = await dispatch(deleteQuestion(slug)); // Pass the correct ID here

        // Check if the action was fulfilled
        if (actionResult.meta.requestStatus === "fulfilled") {
          fetchMasterQuestions(); // Refetch questions after successful deletion
          Swal.fire("Deleted!", "The question has been deleted.", "success");
        } else {
          // Handle failure case
          const errorMessage =
            actionResult.payload?.error || "Failed to delete the question.";
          toast.error(errorMessage);
          console.error("Error response:", actionResult);
        }
      } catch (error) {
        console.error("Error deleting the question", error);
        toast.error(
          "An unexpected error occurred while deleting the question."
        );
      }
    }
  };

  // Function to handle editing an option
  const handleEditOption = (questionId, option) => {
    setSelectedOption(option); // Set the selected option
    setSelectedQuestionSlug(questionId); // Set the selected question ID
    setUpdatedOptionData({
      option_text: option.option_text,
      option_explanation: option.option_explanation || "",
      is_correct: option.is_correct,
    });
    setShowOptionModal(true); // Open the option edit modal
  };

  // Function to handle deleting an option
  const handleDeleteOption = (questionSlug, optionSlug) => {
    console.log("Option ID:", questionSlug); // Log slug
    console.log("Question ID:", optionSlug); // Log slug

    Swal.fire({
      title: "Are you sure?",
      text: "This action cannot be undone!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#ff0000",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, cancel!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const actionResult = await dispatch(
            deleteOption({ questionSlug: questionSlug, optionSlug: optionSlug })
          ); // Pass both questionId and optionId

          // Check if the action was fulfilled
          if (actionResult.meta.requestStatus === "fulfilled") {
            toast.success("Option deleted successfully!");
            fetchMasterQuestions(); // Refetch questions after deletion
          } else {
            // Handle failure case
            const errorMessage =
              actionResult.payload?.error || "Failed to delete the option.";
            toast.error(errorMessage);
            console.error("Error response:", actionResult);
          }
        } catch (error) {
          console.error("Error deleting option", error);
          toast.error(
            "An unexpected error occurred while deleting the option."
          );
        }
      }
    });
  };

  // Function to handle submitting the updated option
  const handleSubmitOption = async (e) => {
    e.preventDefault();

    try {
      const actionResult = await dispatch(
        updateOption({
          questionSlug: selectedQuestionSlug,
          optionSlug: selectedOption.slug, // Pass the option ID to update
          data: {
            option_text: updatedOptionData.option_text, // Updated option text
            option_explanation: updatedOptionData.option_explanation, // Updated option explanation
            is_correct: updatedOptionData.is_correct, // Updated correctness
          },
        })
      );

      // Check if the action was fulfilled
      if (actionResult.meta.requestStatus === "fulfilled") {
        setShowOptionModal(false); // Close the modal after submission
        fetchMasterQuestions(); // Refetch questions to show updated options
        toast.success("Option updated successfully!");
      } else {
        // Handle failure case
        const errorMessage =
          actionResult.payload?.error || "Failed to update the option.";
        toast.error(errorMessage);
        console.error("Error response:", actionResult);
      }
    } catch (error) {
      console.error("Error updating option", error);
      toast.error("An unexpected error occurred while updating the option.");
    }
  };

  return (
    <Container className="mt-1">
      <h2
        className="text-center text-success mb-3"
        style={{ fontSize: "1.5rem" }}
      >
        Master Questions || Add Ques.{" "}
        <small className="h6"> See live updated here. </small>
      </h2>

      {isLoading ? (
        <QuestionListSkeleton number={3}/>
      ) : (
        masterQuestion && (
          <div className="d-flex flex-wrap">
            <Card
              key={masterQuestion.slug}
              className="shadow position-relative"
              style={{
                width: "100%",
                marginBottom: "1rem",
                backgroundColor:
                  masterQuestion.approval_status === "approved"
                    ? "#e6ffee"
                    : masterQuestion.approval_status === "rejected"
                      ? "#ffe6e6"
                      : "#ffffb3", // Default color
              }}
            >
              <Card.Body>
                {/* Option Details */}
                <div>
                  <Card.Title>
                    <div className="d-flex justify-content-between">
                      <div> <IoFilterCircle /> <MathTextRenderer text={masterQuestion.title} /></div>
                      <div className="d-flex align-items-center">
                        <Link to="/master_questions_dashboard">
                          <Button
                            variant="outline-success"
                            className="action-buttons m-1"
                          >
                            <FaMinus size={15} />
                          </Button>
                        </Link>
                        <Button
                          variant="outline-primary"
                          className="action-buttons m-1"
                          onClick={() => handleEdit(masterQuestion)}
                        >
                          <FaEdit size={15} />
                        </Button>
                        {/* <Button
                          variant="outline-danger"
                          className="action-buttons m-1"
                          onClick={() => handleDelete(masterQuestion.slug)}
                        >
                          <FaTrashAlt size={15} />
                        </Button> */}
                      </div>
                    </div>
                  </Card.Title>
                  <Card.Text
                    style={{ marginRight: "0.7rem", textAlign: "justify" }}
                  >
                    <IoEllipsisHorizontalCircleSharp className="fs-6" /> <MathTextRenderer text={masterQuestion.passage_content} />
                  </Card.Text>
                </div>

                {/* Related Questions */}
                {Array.isArray(masterQuestion.questions) &&
                  masterQuestion.questions.length > 0 ? (
                  <div className="mt-2">
                    {masterQuestion.questions.map((question) => (
                      <div key={question.slug} className="mt-1 p-2"
                       style={{
                          marginBottom: "1rem",
                          backgroundColor:
                            question?.approval_status === "approved"
                              ? "#e6ffee"
                              : question.approval_status === "rejected"
                                ? "#ffe6e6"
                                : "#ffffff", // Default color
                        }}
                      >
                        <Card.Title>
                          <span style={{ fontSize: "0.7rem" }}>
                            Subject: {question.subject_name} | Topic:
                            {question.topic_name} | Sub Topic:
                            {question.sub_topic_name}
                          </span>
                        </Card.Title>

                        <Card.Text>
                          <FaQuestionCircle /> {question.content}
                          <Button
                            variant="outline-success"
                            className="action-buttons m-1"
                            onClick={() => handleEditQuestion(question)} // Pass the entire question object here if needed
                          >
                            <FaEdit size={15} />
                          </Button>
                          <Button
                            variant="outline-danger"
                            className="action-buttons m-1 "
                            onClick={() =>
                              handleDeleteQuestion(question.slug)
                            } // Pass slug along with slug
                          >
                            <FaTrashAlt size={15} />
                          </Button>
                        </Card.Text>

                        {question.attachments && (
                          <Card.Img
                            variant="top"
                            src={`${import.meta.env.VITE_BASE_URL}/${question.attachments}`}
                            className="img-fluid rounded-3 mb-4"
                          />
                        )}
                        {/* Options under the question */}
                        {Array.isArray(question.options) &&
                          question.options.length > 0 ? (
                          <ol
                            style={{
                              listStyleType: "decimal",
                              padding: "0 1.5rem",
                            }}
                          >
                            {question.options.map((option) => (
                              <li
                                key={option.slug}
                                style={{ marginBottom: "0.5rem" }}
                              >
                                <div
                                  style={{
                                    color: option.is_correct
                                      ? "#198754"
                                      : "#dc3545",
                                    fontWeight: "bold",
                                  }}
                                >
                                  <div className="option-text">
                                    <FaRegCircle /> <MathTextRenderer text={option.option_text} />
                                  </div>
                                  {/* Option Image - on next line */}
                                  {option.attachments && (
                                    <div className="option-image mt-3">
                                      <img
                                        src={`${import.meta.env.VITE_BASE_URL}/${option.attachments}`}
                                        alt="Option attachment"
                                        className="img-fluid rounded-3 shadow-sm"
                                        style={{
                                          maxWidth: "250px",
                                          height: "auto",
                                          border: "1px solid #e9ecef"
                                        }}
                                      />
                                    </div>
                                  )}
                                  {/* Option Explanation */}
                                  {option.option_explanation && (
                                    <div className="option-explanation mt-2 p-2 bg-light rounded" style={{ fontSize: "0.9rem" }}>
                                      <strong>Explanation:</strong>
                                      <div className="mt-1">
                                        <MathTextRenderer text={option.option_explanation} />
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <Button
                                  variant="outline-primary"
                                  size="sm"
                                  onClick={() =>
                                    handleEditOption(
                                      question.slug,
                                      option                                     
                                    )
                                  } // Pass slug along with slug
                                  className="ms-2"
                                >
                                  <FaEdit size={12} />
                                </Button>
                                <Button
                                  variant="outline-danger"
                                  size="sm"
                                  onClick={() =>
                                    handleDeleteOption(
                                      question.slug,
                                      option.slug                                 
                                    )
                                  } // Pass slug and slug
                                  className="ms-2"
                                >
                                  <FaTrashAlt size={12} />
                                </Button>
                              </li>
                            ))}
                          </ol>
                        ) : (
                          <p className="text-info">
                            No options available for this question.
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-muted mt-3">
                    No related questions available for this option.
                  </p>
                )}
              </Card.Body>
            </Card>
          </div>
        )
      )}

      {/* Modal for Editing Question*/}
      {showQuestionModal && (
        <Modal
          show={showQuestionModal}
          onHide={() => setShowQuestionModal(false)}
          centered
        >
          <Modal.Header closeButton>
            <Modal.Title>Edit Question</Modal.Title>
          </Modal.Header>
          <Modal.Body>
            <Form onSubmit={handleSubmitQuestion}>
              <Form.Group controlId="content">
                <RichTextEditor
                  label="Question Content"
                  name="content"
                  value={updatedQuestionData.content}
                  onChange={handleQuestionInputChange}
                  placeholder="Enter question content"
                  rows={3}
                  required
                />
              </Form.Group>

              {/* No options section, just the question content */}
              {imagePreview && (
                <Form.Group controlId="questionImage" className="mt-3">
                  {imageSizeText && (
                    <p className="text-success">{imageSizeText}</p>
                  )}

                  {imageError && (
                    <p className="text-danger mb-2">{imageError}</p>
                  )}
                  <Form.Label>Question Image</Form.Label>
                  <Form.Control
                    ref={imageInputRef}
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleImageChange(e)}
                  />
                </Form.Group>
              )}

              {/* Image Preview */}
              {imagePreview && (
                <div className="mb-3">
                  <img
                    src={imagePreview}
                    alt="Preview"
                    style={{
                      width: "100%",
                      maxHeight: "200px",
                      objectFit: "cover",
                    }}
                  />
                </div>
              )}

              <Button
                variant="outline-primary"
                type="submit"
                className="mt-3 w-100"
              >
                Save Changes
              </Button>
            </Form>
          </Modal.Body>
          <Toaster />
        </Modal>
      )}

      {/* model for edit master Question */}

      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>Edit Question</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Form.Group controlId="formTitle">
              <RichTextEditor
                label="Title"
                name="title"
                value={updatedData.title}
                onChange={handleInputChange}
                placeholder="Enter title"
                rows={2}
                required
              />
            </Form.Group>
            <Form.Group controlId="formPassageContent">
              <RichTextEditor
                label="Passage Content"
                name="passage_content"
                value={updatedData.passage_content}
                onChange={handleInputChange}
                placeholder="Enter passage content"
                rows={4}
                required
              />
            </Form.Group>
            <div className="d-flex justify-content-end mt-3">
              <Button
                variant="secondary"
                onClick={() => setShowModal(false)}
                className="mx-2"
              >
                Cancel
              </Button>
              <Button type="submit" variant="primary">
                Save Changes
              </Button>
            </div>
          </Form>
        </Modal.Body>
      </Modal>

      {/* Modal for Editing Option */}
      <OptionEditModal
        show={showOptionModal}
        handleClose={() => setShowOptionModal(false)}
        updatedOptionData={updatedOptionData}
        setUpdatedOptionData={setUpdatedOptionData}
        handleSubmitOption={handleSubmitOption}
      />
    </Container>
  );
}
